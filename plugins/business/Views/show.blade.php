@extends('layouts.app')

@section('title', 'Business Details')

@section('content')
<div class="container mx-auto px-4 py-6" data-business-id="{{ $business->id }}" data-user-id="{{ auth()->id() }}" data-can-manage="{{ auth()->check() && auth()->user()->hasPermission('manage_businesses') ? 'true' : 'false' }}">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $business->name }}</h1>
            <div class="flex space-x-3">
                <a href="{{ route('business.index') }}"
                   class="bg-gray-500 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Businesses
                </a>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('business.edit', $business) }}"
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Business
                    </a>
                @endif
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex sm:space-x-8 px-6" aria-label="Tabs" role="tablist">
                    <button onclick="switchTab('business-info')"
                            id="business-info-tab"
                            class="tab-button border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="true" role="tab">
                        <i class="fas fa-building mr-2"></i>
                        Business Info
                    </button>
                    <button onclick="switchTab('contacts')"
                            id="contacts-tab"
                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="false" role="tab">
                        <i class="fas fa-users mr-2"></i>
                        Contacts ({{ $business->contacts->count() }})
                    </button>
                    <button onclick="switchTab('documents')"
                            id="documents-tab"
                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="false" role="tab">
                        <i class="fas fa-file-alt mr-2"></i>
                        Documents ({{ $business->documents->count() }})
                    </button>
                    <button onclick="switchTab('activity')"
                            id="activity-tab"
                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="false" role="tab">
                        <i class="fas fa-history mr-2"></i>
                        Activity (0)
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
                <!-- Business Info Tab -->
                <div id="business-info-content" class="tab-pane">
                    <div class="space-y-6">
                        <!-- Business Information Section -->
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Business Information</h3>
                            </div>
                            <div class="px-6 py-4">
                                <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Business Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $business->name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                            @if($business->email)
                                                <a href="mailto:{{ $business->email }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">
                                                    {{ $business->email }}
                                                </a>
                                            @else
                                                <span class="text-gray-400">Not provided</span>
                                            @endif
                                        </dd>
                                    </div>
                                    @if($business->description)
                                        <div class="md:col-span-2">
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $business->description }}</dd>
                                        </div>
                                    @endif
                                    @if($business->secondary_phone)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Secondary Phone</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                                <a href="tel:{{ $business->secondary_phone }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">
                                                    {{ $business->secondary_phone }}
                                                </a>
                                            </dd>
                                        </div>
                                    @endif
                                    @if($business->secondary_website)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Secondary Website</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                                <a href="{{ $business->secondary_website }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">
                                                    {{ $business->secondary_website }}
                                                    <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                                </a>
                                            </dd>
                                        </div>
                                    @endif
                                    @if($business->address)
                                        <div class="md:col-span-2">
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $business->address }}</dd>
                                        </div>
                                    @endif
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Business Status</dt>
                                        <dd class="mt-1">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                {{ ucfirst($business->status ?? 'Active') }}
                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created By</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                            {{ $business->creator->name ?? 'Administrator' }}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created Date</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $business->created_at->format('M d, Y') }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <!-- Tags & Products Section -->
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Tags & Products</h3>
                            </div>
                            <div class="px-6 py-4 space-y-6">
                                <!-- Business Tags -->
                                <div>
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Business Tags</h4>
                                        @if(auth()->user()->hasPermission('manage_businesses'))
                                            <a href="#" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800">Manage Tags</a>
                                        @endif
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        @if($business->tags && $business->tags->count() > 0)
                                            @foreach($business->tags as $tag)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    {{ $tag->name }}
                                                </span>
                                            @endforeach
                                        @else
                                            <!-- Default tags for demo -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                Enterprise
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                SaaS
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Business Products -->
                                <div>
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Business Products</h4>
                                        @if(auth()->user()->hasPermission('manage_businesses'))
                                            <a href="#" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800">Manage Products</a>
                                        @endif
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        @if($business->products && $business->products->count() > 0)
                                            @foreach($business->products as $product)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                    {{ $product->name }}
                                                </span>
                                            @endforeach
                                        @else
                                            <!-- Default product for demo -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                whatsapp business api
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Danger Zone -->
                        @if(auth()->user()->hasPermission('manage_businesses'))
                            <div class="bg-white dark:bg-gray-800 border border-red-200 dark:border-red-700 rounded-lg">
                                <div class="px-6 py-4 border-b border-red-200 dark:border-red-700">
                                    <h3 class="text-lg font-medium text-red-900 dark:text-red-400">Danger Zone</h3>
                                </div>
                                <div class="px-6 py-4">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        Permanently delete this business and all associated data. This action cannot be undone.
                                    </p>
                                    <form method="POST" action="{{ route('business.destroy', $business) }}"
                                          onsubmit="return confirm('Are you sure you want to delete this business? This action cannot be undone.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                            <i class="fas fa-trash mr-2"></i>
                                            Delete Business
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Contacts Tab -->
                <div id="contacts-content" class="tab-pane hidden">
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Business Contacts</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <span id="contacts-count">{{ $business->contacts->count() }}</span>
                                <span id="contacts-total">of {{ $business->contacts->count() }}</span> contacts
                            </p>
                        </div>
                        @if(auth()->user()->hasPermission('manage_businesses'))
                            <div class="flex space-x-2">
                                <button onclick="showContactSearch()"
                                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out">
                                    <i class="fas fa-search mr-1"></i>
                                    Add Existing Contact
                                </button>
                                <a href="{{ route('business.contacts.create', $business) }}"
                                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out">
                                    <i class="fas fa-plus mr-1"></i>
                                    Add New Contact
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Search Current Contacts -->
                    <div class="mb-4">
                        <div class="relative">
                            <input type="text"
                                   id="current_contacts_search"
                                   placeholder="Search current contacts by name, email, phone, position..."
                                   class="w-full px-3 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm">
                            <div class="absolute inset-y-0 right-0 flex items-center">
                                <button type="button"
                                        id="clear_current_search"
                                        onclick="clearCurrentContactsSearch()"
                                        class="hidden mr-1 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                                <div class="pr-3">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add External Contact Search Section (Initially Hidden) -->
                    <div id="contact-search-section" class="hidden mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h4 class="text-md font-medium text-gray-900 dark:text-white">Search Existing Contacts</h4>
                            <button onclick="hideContactSearch()" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="mb-3">
                            <div class="relative">
                                <input type="text"
                                       id="contact_search_input"
                                       placeholder="Search by name, email, phone, position..."
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white text-sm">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Search Results -->
                        <div id="contact_search_results" class="hidden">
                            <div class="border border-gray-300 dark:border-gray-600 rounded-md max-h-48 overflow-y-auto bg-white dark:bg-gray-800">
                                <!-- Results will be populated here -->
                            </div>
                        </div>

                        <!-- No Results Message -->
                        <div id="contact_no_results" class="hidden text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400 text-sm">No contacts found matching your search.</p>
                        </div>
                    </div>

                    @if($business->contacts->count() > 0)
                        <div id="contacts-list" class="space-y-4">
                            @foreach($business->contacts as $contact)
                                <div class="contact-item bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                                     data-name="{{ strtolower($contact->name) }}"
                                     data-arabic-name="{{ strtolower($contact->arabic_name ?? '') }}"
                                     data-email="{{ strtolower($contact->email ?? '') }}"
                                     data-phone="{{ $contact->phone ?? '' }}"
                                     data-phone2="{{ $contact->phone2 ?? '' }}"
                                     data-position="{{ strtolower($contact->position ?? '') }}"
                                     data-department="{{ strtolower($contact->department ?? '') }}">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-4">
                                            <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $contact->name }}
                                                @if($contact->is_primary)
                                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                                        Primary
                                                    </span>
                                                @endif
                                            </h4>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $contact->position ?: 'No position specified' }}</p>
                                            <div class="flex items-center space-x-4 mt-1">
                                                @if($contact->email)
                                                    <span class="text-xs text-gray-400">
                                                        <i class="fas fa-envelope mr-1"></i>{{ $contact->email }}
                                                    </span>
                                                @endif
                                                @if($contact->phone)
                                                    <span class="text-xs text-gray-400">
                                                        <i class="fas fa-phone mr-1"></i>{{ $contact->phone }}
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No contacts added yet.</p>
                        </div>
                    @endif
                </div>

                <!-- Documents Tab -->
                <div id="documents-content" class="tab-pane hidden">
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Business Documents</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $business->documents->count() }} documents</p>
                        </div>
                        @if(auth()->user()->hasPermission('manage_businesses'))
                            <div class="flex space-x-2">
                                <a href="{{ route('business.documents.index', $business) }}"
                                   class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out">
                                    <i class="fas fa-list mr-1"></i>
                                    View All
                                </a>
                                <a href="{{ route('business.documents.create', $business) }}"
                                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out">
                                    <i class="fas fa-plus mr-1"></i>
                                    Upload Document
                                </a>
                            </div>
                        @endif
                    </div>

                    @if($business->documents->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($business->documents->take(6) as $document)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-start justify-between">
                                        <div class="flex items-center flex-1 min-w-0">
                                            <div class="flex-shrink-0">
                                                <div class="h-10 w-10 rounded-lg bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                                    <i class="{{ $document->file_icon ?? 'fas fa-file' }} text-gray-600 dark:text-gray-400"></i>
                                                </div>
                                            </div>
                                            <div class="ml-3 flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate" title="{{ $document->original_name }}">
                                                    {{ $document->original_name }}
                                                </p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ $document->document_type_label ?? 'Unknown Type' }}
                                                </p>
                                                <p class="text-xs text-gray-400 mt-1">
                                                    {{ $document->formatted_file_size }} • {{ $document->upload_date->diffForHumans() }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-1 ml-2">
                                            @if($document->isImage() || $document->isPdf())
                                                <a href="{{ route('business.documents.view', [$business, $document]) }}"
                                                   target="_blank"
                                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs"
                                                   title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            @endif
                                            <a href="{{ route('business.documents.download', [$business, $document]) }}"
                                               class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-xs"
                                               title="Download">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            @if(auth()->user()->hasPermission('manage_businesses'))
                                                <a href="{{ route('business.documents.edit', [$business, $document]) }}"
                                                   class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 text-xs"
                                                   title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                    @if($document->description)
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-2 truncate" title="{{ $document->description }}">
                                            {{ $document->description }}
                                        </p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                        @if($business->documents->count() > 6)
                            <div class="mt-4 text-center">
                                <a href="{{ route('business.documents.index', $business) }}"
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium">
                                    View all {{ $business->documents->count() }} documents →
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No documents uploaded yet.</p>
                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <div class="mt-4">
                                    <a href="{{ route('business.documents.create', $business) }}"
                                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>
                                        Upload First Document
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Activity Tab -->
                <div id="activity-content" class="tab-pane hidden">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Business Activity</h3>
                        @if(auth()->user()->hasPermission('manage_businesses'))
                            <a href="{{ route('business.activities.create', $business) }}"
                               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-2"></i>
                                Add Activity
                            </a>
                        @endif
                    </div>

                    <!-- Activity Filters -->
                    <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                        <div class="flex flex-wrap gap-4">
                            <div>
                                <label for="activity-type-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                                <select id="activity-type-filter" class="form-select rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white text-sm">
                                    <option value="">All Types</option>
                                    <option value="comment">Comments</option>
                                    <option value="chat">Chat Messages</option>
                                    <option value="email">Emails</option>
                                    <option value="phone">Phone Calls</option>
                                    <option value="meeting">Meetings</option>
                                    <option value="visit">Site Visits</option>
                                    <option value="whatsapp">WhatsApp</option>
                                    <option value="sms">SMS</option>
                                    <option value="status_change">Status Changes</option>
                                    <option value="document">Documents</option>
                                    <option value="system">System</option>
                                </select>
                            </div>
                            <div>
                                <label for="activity-date-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date Range</label>
                                <select id="activity-date-filter" class="form-select rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white text-sm">
                                    <option value="">All Time</option>
                                    <option value="today">Today</option>
                                    <option value="yesterday">Yesterday</option>
                                    <option value="week">This Week</option>
                                    <option value="month">This Month</option>
                                </select>
                            </div>
                            <div class="flex-1">
                                <label for="activity-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
                                <input type="text" id="activity-search" placeholder="Search activities..."
                                       class="form-input rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white text-sm w-full">
                            </div>
                        </div>
                    </div>

                    <!-- Activities Container -->
                    <div id="activities-container">
                        <div id="activities-loading" class="px-6 py-12 text-center" style="display: none;">
                            <i class="fas fa-spinner fa-spin text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">Loading activities...</p>
                        </div>

                        <div id="activities-empty" class="px-6 py-12 text-center">
                            <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No activities yet.</p>
                            <p class="text-gray-400 text-sm">Activity will appear here as you interact with this business.</p>
                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <div class="mt-4">
                                    <a href="{{ route('business.activities.create', $business) }}"
                                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>
                                        Add First Activity
                                    </a>
                                </div>
                            @endif
                        </div>

                        <div id="activities-list" class="space-y-4" style="display: none;">
                            <!-- Activities will be loaded here -->
                        </div>

                        <div id="activities-pagination" class="mt-6" style="display: none;">
                            <!-- Pagination will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Minimal, safe JavaScript -->
<script>
// Minimal switchTab function - defined first to ensure availability
function switchTab(tabName) {
    console.log('switchTab called with:', tabName);
    
    // Hide all tab panes
    const tabPanes = document.querySelectorAll('.tab-pane');
    tabPanes.forEach(pane => {
        pane.classList.add('hidden');
    });

    // Remove active state from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        button.setAttribute('aria-selected', 'false');
    });

    // Show selected tab pane
    const selectedPane = document.getElementById(tabName + '-content');
    if (selectedPane) {
        selectedPane.classList.remove('hidden');
    }

    // Add active state to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        selectedButton.classList.add('border-blue-500', 'text-blue-600');
        selectedButton.setAttribute('aria-selected', 'true');
    }

    // Update URL hash
    window.location.hash = tabName;

    // Load activities when activity tab is selected
    if (tabName === 'activity') {
        // Small delay to ensure tab is visible
        setTimeout(() => {
            const activitiesList = document.getElementById('activities-list');
            if (activitiesList && activitiesList.children.length === 0) {
                loadActivities(1);
            }
        }, 100);
    }
}

// Simple initialization
document.addEventListener('DOMContentLoaded', function() {

    // Set default tab
    const hash = window.location.hash.substring(1);
    if (hash && ['business-info', 'contacts', 'documents', 'activity'].includes(hash)) {
        switchTab(hash);
    } else {
        switchTab('business-info');
    }

    // Contact search functionality
    initializeContactSearch();
    initializeCurrentContactsFilter();

    // Activities functionality
    initializeActivities();
});

function initializeCurrentContactsFilter() {
    const currentContactsSearch = document.getElementById('current_contacts_search');
    const clearButton = document.getElementById('clear_current_search');

    if (currentContactsSearch) {
        currentContactsSearch.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();
            filterCurrentContacts(query);

            // Show/hide clear button
            if (this.value.length > 0) {
                clearButton.classList.remove('hidden');
            } else {
                clearButton.classList.add('hidden');
            }
        });
    }
}

function clearCurrentContactsSearch() {
    const currentContactsSearch = document.getElementById('current_contacts_search');
    const clearButton = document.getElementById('clear_current_search');

    if (currentContactsSearch) {
        currentContactsSearch.value = '';
        clearButton.classList.add('hidden');
        filterCurrentContacts('');
        currentContactsSearch.focus();
    }
}

function filterCurrentContacts(query) {
    const contactItems = document.querySelectorAll('.contact-item');
    let visibleCount = 0;

    contactItems.forEach(item => {
        if (query === '') {
            item.style.display = 'block';
            visibleCount++;
        } else {
            const name = item.dataset.name || '';
            const arabicName = item.dataset.arabicName || '';
            const email = item.dataset.email || '';
            const phone = item.dataset.phone || '';
            const phone2 = item.dataset.phone2 || '';
            const position = item.dataset.position || '';
            const department = item.dataset.department || '';

            const matches = name.includes(query) ||
                          arabicName.includes(query) ||
                          email.includes(query) ||
                          phone.includes(query) ||
                          phone2.includes(query) ||
                          position.includes(query) ||
                          department.includes(query);

            if (matches) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        }
    });

    // Update contact count
    const contactsCountElement = document.getElementById('contacts-count');
    if (contactsCountElement) {
        contactsCountElement.textContent = visibleCount;
    }

    // Show/hide no results message for current contacts
    const contactsList = document.getElementById('contacts-list');
    const existingNoResults = document.getElementById('current-contacts-no-results');

    if (existingNoResults) {
        existingNoResults.remove();
    }

    if (visibleCount === 0 && query !== '') {
        const noResultsDiv = document.createElement('div');
        noResultsDiv.id = 'current-contacts-no-results';
        noResultsDiv.className = 'text-center py-8';
        noResultsDiv.innerHTML = `
            <i class="fas fa-search text-gray-400 text-3xl mb-3"></i>
            <p class="text-gray-500 dark:text-gray-400">No contacts found matching "${query}"</p>
            <button onclick="clearCurrentContactsSearch()" class="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm">
                Clear search
            </button>
        `;
        contactsList.appendChild(noResultsDiv);
    }
}

function initializeContactSearch() {
    const searchInput = document.getElementById('contact_search_input');
    const searchResults = document.getElementById('contact_search_results');
    const noResults = document.getElementById('contact_no_results');

    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                searchResults.classList.add('hidden');
                noResults.classList.add('hidden');
                return;
            }

            searchTimeout = setTimeout(() => {
                searchContacts(query);
            }, 300);
        });
    }

    function searchContacts(query) {
        fetch(`{{ route('business.contacts.search-contacts', $business) }}?search=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displayContactSearchResults(data.contacts);
            })
            .catch(error => {
                console.error('Contact search error:', error);
            });
    }

    function displayContactSearchResults(contacts) {
        if (contacts.length === 0) {
            searchResults.classList.add('hidden');
            noResults.classList.remove('hidden');
            return;
        }

        noResults.classList.add('hidden');

        const resultsHtml = contacts.map(contact => `
            <div class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                 onclick="showContactActionModal(${contact.id}, '${contact.name}', '${contact.arabic_name || ''}', '${contact.email || ''}', '${contact.contact_info || ''}', '${contact.position || ''}', '${contact.department || ''}', '${contact.business_name}')">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900 dark:text-white text-sm">
                            ${contact.display_name}
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            ${contact.position ? contact.position : 'No position'}
                            ${contact.department ? ` - ${contact.department}` : ''}
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            ${contact.contact_info || 'No contact info'}
                        </div>
                        <div class="text-xs text-gray-400 dark:text-gray-500">
                            From: ${contact.business_name}
                        </div>
                    </div>
                    <div class="text-green-600 dark:text-green-400 ml-2">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                </div>
            </div>
        `).join('');

        searchResults.querySelector('div').innerHTML = resultsHtml;
        searchResults.classList.remove('hidden');
    }
}

function showContactSearch() {
    document.getElementById('contact-search-section').classList.remove('hidden');
    document.getElementById('contact_search_input').focus();
}

function hideContactSearch() {
    document.getElementById('contact-search-section').classList.add('hidden');
    document.getElementById('contact_search_input').value = '';
    document.getElementById('contact_search_results').classList.add('hidden');
    document.getElementById('contact_no_results').classList.add('hidden');
}

function showContactActionModal(id, name, arabicName, email, contactInfo, position, department, businessName) {
    // Create modal HTML
    const modalHtml = `
        <div id="contactActionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Add ${name}</h3>
                        <button onclick="closeContactModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4">
                        <div class="font-medium text-gray-900 dark:text-white">${name}${arabicName ? ` (${arabicName})` : ''}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">${position || 'No position'}${department ? ` - ${department}` : ''}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">${contactInfo || 'No contact info'}</div>
                        <div class="text-xs text-gray-400 dark:text-gray-500">Currently at: ${businessName}</div>
                    </div>

                    <form method="POST" action="{{ route('business.contacts.copy-contact', $business) }}">
                        @csrf
                        <input type="hidden" name="contact_id" value="${id}">

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Action</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="copy_mode" value="copy" checked class="mr-2">
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        <strong>Copy</strong> - Create a duplicate contact
                                    </span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="copy_mode" value="move" class="mr-2">
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        <strong>Move</strong> - Transfer contact to this business
                                    </span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeContactModal()"
                                    class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                Add Contact
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Add click outside to close
    document.getElementById('contactActionModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeContactModal();
        }
    });
}

function closeContactModal() {
    const modal = document.getElementById('contactActionModal');
    if (modal) {
        modal.remove();
    }
}

// Activities functionality
let activitiesCurrentPage = 1;
let activitiesLoading = false;
let activitiesFilters = {
    message_type: '',
    date_range: '',
    search: ''
};

function initializeActivities() {
    // Initialize filter event listeners
    const typeFilter = document.getElementById('activity-type-filter');
    const dateFilter = document.getElementById('activity-date-filter');
    const searchInput = document.getElementById('activity-search');

    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            activitiesFilters.message_type = this.value;
            loadActivities(1);
        });
    }

    if (dateFilter) {
        dateFilter.addEventListener('change', function() {
            activitiesFilters.date_range = this.value;
            loadActivities(1);
        });
    }

    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                activitiesFilters.search = this.value;
                loadActivities(1);
            }, 300);
        });
    }

    // Load activities if activity tab is already active (from URL hash)
    if (window.location.hash === '#activity') {
        setTimeout(() => loadActivities(1), 100);
    }
}

function loadActivities(page = 1) {
    if (activitiesLoading) {
        return;
    }

    activitiesLoading = true;
    activitiesCurrentPage = page;

    // Show loading state
    const loadingDiv = document.getElementById('activities-loading');
    const emptyDiv = document.getElementById('activities-empty');
    const listDiv = document.getElementById('activities-list');
    const paginationDiv = document.getElementById('activities-pagination');

    if (loadingDiv) loadingDiv.style.display = 'block';
    if (emptyDiv) emptyDiv.style.display = 'none';
    if (listDiv) listDiv.style.display = 'none';
    if (paginationDiv) paginationDiv.style.display = 'none';

    // Build query parameters
    const params = new URLSearchParams({
        page: page,
        per_page: 20,
        ...activitiesFilters
    });

    // Remove empty parameters
    for (const [key, value] of params.entries()) {
        if (!value) {
            params.delete(key);
        }
    }

    const url = `{{ route('business.activities.index', $business) }}?${params.toString()}`;

    fetch(url, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        activitiesLoading = false;
        if (loadingDiv) loadingDiv.style.display = 'none';

        if (data.success && data.data && data.data.length > 0) {
            displayActivities(data.data);
            displayActivitiesPagination(data.pagination);
            if (listDiv) listDiv.style.display = 'block';
            if (paginationDiv) paginationDiv.style.display = 'block';
        } else {
            if (emptyDiv) emptyDiv.style.display = 'block';
        }
    })
    .catch(error => {
        activitiesLoading = false;
        if (loadingDiv) loadingDiv.style.display = 'none';
        if (emptyDiv) emptyDiv.style.display = 'block';
    });
}

function displayActivities(activities) {
    const listDiv = document.getElementById('activities-list');
    listDiv.innerHTML = '';

    activities.forEach(activity => {
        const activityHtml = createActivityHtml(activity);
        listDiv.appendChild(activityHtml);
    });
}

function createActivityHtml(activity) {
    const div = document.createElement('div');
    div.className = 'bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700';

    const typeInfo = getActivityTypeInfo(activity.type);
    const date = new Date(activity.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });

    div.innerHTML = `
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-${typeInfo.color}-100 dark:bg-${typeInfo.color}-900 rounded-full flex items-center justify-center">
                    <i class="${typeInfo.icon} text-${typeInfo.color}-600 dark:text-${typeInfo.color}-400"></i>
                </div>
            </div>
            <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                        ${activity.title}
                    </h4>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${typeInfo.color}-100 dark:bg-${typeInfo.color}-900 text-${typeInfo.color}-800 dark:text-${typeInfo.color}-200">
                            ${activity.category || activity.type}
                        </span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">${date}</span>
                    </div>
                </div>
                ${activity.description ? `<p class="mt-1 text-sm text-gray-600 dark:text-gray-300">${activity.description}</p>` : ''}
                <div class="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <span>By ${activity.user ? activity.user.name : 'System'}</span>
                    ${activity.is_system_generated ? '<span class="ml-2 px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">System</span>' : ''}
                </div>
            </div>
        </div>
    `;

    return div;
}

function getActivityTypeInfo(type) {
    const types = {
        'comment': { icon: 'fas fa-comment', color: 'blue' },
        'chat': { icon: 'fas fa-comments', color: 'green' },
        'email': { icon: 'fas fa-envelope', color: 'purple' },
        'phone': { icon: 'fas fa-phone', color: 'orange' },
        'meeting': { icon: 'fas fa-calendar', color: 'indigo' },
        'visit': { icon: 'fas fa-map-marker-alt', color: 'red' },
        'whatsapp': { icon: 'fab fa-whatsapp', color: 'green' },
        'sms': { icon: 'fas fa-sms', color: 'yellow' },
        'status_change': { icon: 'fas fa-exchange-alt', color: 'blue' },
        'document_upload': { icon: 'fas fa-file-upload', color: 'gray' },
        'document_delete': { icon: 'fas fa-file-times', color: 'red' },
        'system_log': { icon: 'fas fa-cog', color: 'gray' },
        'manual_log': { icon: 'fas fa-edit', color: 'blue' }
    };

    return types[type] || { icon: 'fas fa-circle', color: 'gray' };
}

function displayActivitiesPagination(pagination) {
    const paginationDiv = document.getElementById('activities-pagination');

    if (pagination.last_page <= 1) {
        if (paginationDiv) paginationDiv.style.display = 'none';
        return;
    }

    let paginationHtml = '<div class="flex items-center justify-between">';
    paginationHtml += `<div class="text-sm text-gray-700 dark:text-gray-300">
        Showing page ${pagination.current_page} of ${pagination.last_page} (${pagination.total} total)
    </div>`;

    paginationHtml += '<div class="flex space-x-2">';

    // Previous button
    if (pagination.current_page > 1) {
        paginationHtml += `<button onclick="loadActivities(${pagination.current_page - 1})"
            class="px-3 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600">
            Previous
        </button>`;
    }

    // Next button
    if (pagination.has_more) {
        paginationHtml += `<button onclick="loadActivities(${pagination.current_page + 1})"
            class="px-3 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600">
            Next
        </button>`;
    }

    paginationHtml += '</div></div>';
    paginationDiv.innerHTML = paginationHtml;
}
</script>
@endsection
